#!/usr/bin/env python3
"""
Performance test for SVG caching system.

This script demonstrates the performance benefits of using the SVG cache
compared to loading SVG files directly each time.
"""

import time
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GdkPixbuf

from utils.svg_cache import get_cached_pixbuf, clear_svg_cache, get_svg_cache_stats


def find_test_svg_files():
    """Find some SVG files to use for testing."""
    svg_files = []
    
    # Look for SVG files in the config/assets/icons directory
    icons_dir = project_root / "config" / "assets" / "icons"
    if icons_dir.exists():
        svg_files.extend(list(icons_dir.glob("*.svg")))
        
        # Also look in subdirectories
        for subdir in icons_dir.iterdir():
            if subdir.is_dir():
                svg_files.extend(list(subdir.glob("*.svg")))
    
    return svg_files[:10]  # Limit to first 10 files for testing


def test_direct_loading(svg_files, iterations=100):
    """Test loading SVG files directly without caching."""
    print(f"Testing direct loading ({iterations} iterations)...")
    
    start_time = time.time()
    
    for i in range(iterations):
        for svg_file in svg_files:
            try:
                # Load at different sizes to simulate real usage
                sizes = [(16, 16), (24, 24), (32, 32), (48, 48)]
                for width, height in sizes:
                    pixbuf = GdkPixbuf.Pixbuf.new_from_file_at_scale(
                        str(svg_file), width, height, True
                    )
                    # Simulate using the pixbuf
                    _ = pixbuf.get_width()
            except Exception as e:
                print(f"Error loading {svg_file}: {e}")
    
    end_time = time.time()
    return end_time - start_time


def test_cached_loading(svg_files, iterations=100):
    """Test loading SVG files with caching."""
    print(f"Testing cached loading ({iterations} iterations)...")
    
    # Clear cache to start fresh
    clear_svg_cache()
    
    start_time = time.time()
    
    for i in range(iterations):
        for svg_file in svg_files:
            try:
                # Load at different sizes to simulate real usage
                sizes = [(16, 16), (24, 24), (32, 32), (48, 48)]
                for size in sizes:
                    pixbuf = get_cached_pixbuf(str(svg_file), size)
                    if pixbuf:
                        # Simulate using the pixbuf
                        _ = pixbuf.get_width()
            except Exception as e:
                print(f"Error loading {svg_file}: {e}")
    
    end_time = time.time()
    return end_time - start_time


def main():
    """Run the performance comparison."""
    print("SVG Cache Performance Test")
    print("=" * 50)
    
    # Initialize GTK (required for GdkPixbuf operations)
    Gtk.init([])
    
    # Find test files
    svg_files = find_test_svg_files()
    
    if not svg_files:
        print("No SVG files found for testing!")
        print("Make sure you have SVG files in config/assets/icons/")
        return
    
    print(f"Found {len(svg_files)} SVG files for testing:")
    for svg_file in svg_files:
        print(f"  - {svg_file.name}")
    print()
    
    iterations = 50  # Reduced for faster testing
    
    # Test direct loading
    direct_time = test_direct_loading(svg_files, iterations)
    print(f"Direct loading time: {direct_time:.3f} seconds")
    
    # Test cached loading
    cached_time = test_cached_loading(svg_files, iterations)
    print(f"Cached loading time: {cached_time:.3f} seconds")
    
    # Show cache statistics
    stats = get_svg_cache_stats()
    print(f"\nCache statistics:")
    print(f"  Total entries: {stats['total_entries']}")
    print(f"  Alive entries: {stats['alive_entries']}")
    print(f"  Dead entries: {stats['dead_entries']}")
    print(f"  Tracked files: {stats['tracked_files']}")
    
    # Calculate performance improvement
    if cached_time > 0:
        improvement = ((direct_time - cached_time) / direct_time) * 100
        speedup = direct_time / cached_time
        
        print(f"\nPerformance Results:")
        print(f"  Time saved: {direct_time - cached_time:.3f} seconds")
        print(f"  Performance improvement: {improvement:.1f}%")
        print(f"  Speedup factor: {speedup:.1f}x")
        
        if improvement > 0:
            print(f"  ✅ Caching provides significant performance benefits!")
        else:
            print(f"  ⚠️  Caching overhead detected (normal for small datasets)")
    
    print(f"\nNote: Performance benefits are most noticeable with:")
    print(f"  - Frequent icon updates (like status indicators)")
    print(f"  - Large numbers of SVG files")
    print(f"  - Complex SVG files")
    print(f"  - Real-world usage patterns")


if __name__ == "__main__":
    main()
