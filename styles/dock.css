#dock {
  background-color: alpha(var(--shadow), 0.4);
  padding: 8px;
  margin: 8px 8px 5px 8px;
  border-radius: 16px;
  /* border: 2px solid var(--surface); */
  transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#dock.vertical {
  margin: 8px 0 8px 8px;
  border-radius: 20px 0 0 20px;
}

#dock.vertical.left {
  margin: 8px 8px 8px 0;
  border-radius: 0 20px 20px 0;
}

#dock-full {
  transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 1;
}

#dock-separator {
  padding: 2px;
  border-radius: 16px;
  background-color: var(--surface-bright);
}

#dock-app-button {
  padding: 4px;
  border-radius: 24px;
  /* box-shadow: 0 0 4px alpha(var(--shadow), 0.5); */
  transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#dock-app-button:hover {
  background-color: var(--surface-bright);
}

#dock-app-button:hover.instance {
  background-color: var(--outline);
}

#dock-app-button:active,
#dock-app-button:active.instance {
  background-color: var(--primary);
}

#dock-app-button.instance {
  border-radius: 12px;
}

#dock-app-dot {
  background-color: var(--primary);
  border-radius: 50%;
  margin-top: 4px;
  min-width: 4px;
  min-height: 4px;
}

#dock-corner-left {
  margin: 0 -8px 0 0;
}

#dock-corner-right {
  margin: 0 0 0 -8px;
}

#dock-corner-top {
  margin: 0 0 -8px 0;
}

#dock-corner-bottom {
  margin: -8px 0 0 0;
}
