/* MenuBar Styles - Simple version */

/* Main menubar container */
#menubar {
    background: transparent;
    border: none;
    padding: 0px;
    margin: 0px;
}

/* System button */
#envsh-button {
    background: transparent;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    margin: 0px 2px;
    color: var(--foreground);
    font-weight: 500;
    font-size: 13px;
    min-height: 20px;
}

#envsh-button:hover {
    background: var(--surface-bright);
    color: var(--primary);
}

/* Global menu buttons */
#global-title-button,
#global-menu-button-file,
#global-menu-button-edit,
#global-menu-button-view,
#global-menu-button-go,
#global-menu-button-window,
#global-menu-button-help {
    background: transparent;
    border: none;
    border-radius: 4px;
    padding: 4px 12px;
    margin: 0px 1px;
    color: var(--foreground);
    font-size: 13px;
    font-weight: 400;
    min-height: 20px;
}

/* Hover states */
#global-title-button:hover,
#global-menu-button-file:hover,
#global-menu-button-edit:hover,
#global-menu-button-view:hover,
#global-menu-button-go:hover,
#global-menu-button-window:hover,
#global-menu-button-help:hover {
    background: var(--surface-bright);
    color: var(--primary);
}

/* Title button special styling */
#global-title-button {
    font-weight: 600;
    color: var(--primary);
}

/* Dropdown menu styling */
#dropdown-menu {
    background: transparent;
    border: none;
    border-radius: 8px;
    padding: 0;
    margin: 4px 0;
}

/* Dropdown options container */
#dropdown-options {
    background: var(--surface);
    border: 1px solid var(--outline);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow);
    padding: 8px;
    min-width: 200px;
}

/* Dropdown options */
#dropdown-option {
    background: transparent;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 2px;
    color: var(--foreground);
    font-size: 13px;
    transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    min-height: 24px;
}

#dropdown-option:hover {
    background: var(--primary);
    color: var(--on-primary);
    transition: all 100ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Legacy dropdown compatibility */
#dropdown-content {
    background-color: var(--surface);
    border: 1px solid var(--outline);
    border-radius: 8px;
}

#dropdown-item {
    border-radius: 4px;
    padding: 8px 12px;
    margin: 1px 4px;
}

#dropdown-item:hover {
    background-color: var(--primary);
    color: var(--on-primary);
}
