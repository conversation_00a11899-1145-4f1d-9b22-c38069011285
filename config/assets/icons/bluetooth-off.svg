<svg width="32" height="32" version="1.1" xmlns="http://www.w3.org/2000/svg">
 <style id="current-color-scheme" type="text/css">.ColorScheme-Text {
        color:#dedede;
      }</style>
 <path class="ColorScheme-Text" d="m16.25 4.9375c-0.0985-1.54e-4 -0.19605 0.01909-0.28711 0.05664-0.28036 0.1162-0.46307 0.38988-0.46289 0.69336v8.1847l-5.2422-4.8223c-0.14642-0.13464-0.34032-0.2056-0.53906-0.19726-0.19881 0.0077-0.3864 0.09416-0.52148 0.24023-0.2808 0.30514-0.26068 0.78023 0.0449 1.0605l6.2578 5.7578v0.17969l-6.2578 5.7559c-0.3043 0.28012-0.32438 0.75371-0.0449 1.0586 0.28031 0.30558 0.7554 0.32571 1.0605 0.04492l5.2422-4.8223v8.1845c-1e-3 0.66959 0.80849 1.0054 1.2812 0.53125l6-5.3134c0.30283-0.3033 0.29038-0.79824-0.0273-1.0859l-4.8907-4.4453 4.8906-4.4453c0.31768-0.2877 0.33013-0.78264 0.0273-1.0859l-6-5.3116c-0.14102-0.14052-0.33213-0.21922-0.53121-0.21875zm0.75 2.5625 4.1621 3.4737-4.1621 3.7852zm0 9.7394 4.1621 3.7852-4.1621 3.4755z" fill="currentColor" opacity=".35"/>
 <rect class="ColorScheme-Text" transform="rotate(45)" x="6.6274" y="-.75" width="32" height="1.5" rx=".75" ry=".75" fill="currentColor"/>
</svg>
