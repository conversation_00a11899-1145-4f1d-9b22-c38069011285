{"todos": [{"id": 14, "title": "yo", "description": "", "priority": "low", "category": "", "due_date": null, "completed": false, "created": "2025-06-26T16:51:06.643095", "completed_at": null}, {"id": 16, "title": "Medium priority task", "description": "", "priority": "medium", "category": "normal", "due_date": null, "completed": false, "created": "2025-06-26T16:52:59.579365", "completed_at": null}, {"id": 17, "title": "Low priority task", "description": "", "priority": "low", "category": "later", "due_date": null, "completed": false, "created": "2025-06-26T16:52:59.583278", "completed_at": null}, {"id": 18, "title": "🔥 URGENT: Fix critical bug", "description": "", "priority": "high", "category": "work", "due_date": null, "completed": true, "created": "2025-06-26T16:53:51.412081", "completed_at": "2025-06-26T16:53:51.418575"}, {"id": 19, "title": "📝 Review documentation", "description": "", "priority": "medium", "category": "work", "due_date": null, "completed": false, "created": "2025-06-26T16:53:51.413594", "completed_at": null}, {"id": 20, "title": "🧹 Clean desk", "description": "", "priority": "low", "category": "personal", "due_date": null, "completed": false, "created": "2025-06-26T16:53:51.415181", "completed_at": null}, {"id": 21, "title": "Test high priority", "description": "", "priority": "high", "category": "test", "due_date": null, "completed": true, "created": "2025-06-26T16:55:12.629834", "completed_at": "2025-06-26T17:00:48.989919"}, {"id": 22, "title": "Test medium priority", "description": "", "priority": "medium", "category": "test", "due_date": null, "completed": false, "created": "2025-06-26T16:55:12.631496", "completed_at": null}, {"id": 23, "title": "Test low priority", "description": "", "priority": "low", "category": "test", "due_date": null, "completed": false, "created": "2025-06-26T16:55:12.632521", "completed_at": null}, {"id": 24, "title": "High priority task", "description": "", "priority": "high", "category": "test", "due_date": null, "completed": true, "created": "2025-06-26T16:57:15.780304", "completed_at": "2025-06-26T16:57:15.800393"}, {"id": 25, "title": "Medium priority task", "description": "", "priority": "medium", "category": "test", "due_date": null, "completed": false, "created": "2025-06-26T16:57:15.783358", "completed_at": null}, {"id": 26, "title": "Low priority task", "description": "", "priority": "low", "category": "test", "due_date": null, "completed": false, "created": "2025-06-26T16:57:15.792488", "completed_at": null}, {"id": 27, "title": "🔥 High priority", "description": "", "priority": "high", "category": "test", "due_date": null, "completed": true, "created": "2025-06-26T16:57:50.943800", "completed_at": "2025-06-26T17:00:50.482300"}, {"id": 28, "title": "📝 Medium priority", "description": "", "priority": "medium", "category": "test", "due_date": null, "completed": false, "created": "2025-06-26T16:57:50.952343", "completed_at": null}, {"id": 29, "title": "🧹 Low priority", "description": "", "priority": "low", "category": "test", "due_date": null, "completed": false, "created": "2025-06-26T16:57:50.968381", "completed_at": null}], "next_id": 30, "last_modified": "2025-06-26T17:00:50.482384"}