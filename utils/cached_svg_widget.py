"""
Enhanced SVG widget with built-in caching support.

This module provides an enhanced SVG widget that automatically uses the
SVG caching system for improved performance.
"""

from typing import Optional, Tuple
from fabric.widgets.svg import Svg
from utils.svg_cache import set_svg_from_cached_file, CachedSvgMixin


class CachedSvg(Svg, CachedSvgMixin):
    """
    SVG widget with built-in caching support.
    
    This widget extends the standard Fabric SVG widget to automatically
    use the SVG caching system, providing better performance for frequently
    updated SVG icons.
    
    Usage:
        # Drop-in replacement for Svg widget
        icon = CachedSvg(
            name="my-icon",
            size=24,
            svg_file="/path/to/icon.svg"
        )
        
        # Use cached loading methods
        icon.set_from_cached_file("/path/to/new-icon.svg", (24, 24))
    """
    
    def __init__(self, svg_file: str = None, cache_size: Optional[Tuple[int, int]] = None, **kwargs):
        """
        Initialize the cached SVG widget.
        
        Args:
            svg_file: Path to the SVG file
            cache_size: Optional tuple of (width, height) for caching
            **kwargs: Additional arguments passed to the parent Svg widget
        """
        super().__init__(**kwargs)
        
        self._cache_size = cache_size
        
        # If an SVG file is provided, load it with caching
        if svg_file:
            self.set_from_cached_file(svg_file, cache_size)
    
    def set_from_file(self, svg_path: str):
        """
        Override set_from_file to use caching by default.
        
        This method automatically uses the caching system while maintaining
        compatibility with the original API.
        """
        self.set_from_cached_file(svg_path, self._cache_size)
    
    def set_cache_size(self, size: Tuple[int, int]):
        """
        Set the default cache size for this widget.
        
        Args:
            size: Tuple of (width, height) for caching
        """
        self._cache_size = size
    
    def get_cache_size(self) -> Optional[Tuple[int, int]]:
        """Get the current cache size setting."""
        return self._cache_size


class SmartCachedSvg(CachedSvg):
    """
    Smart SVG widget that automatically determines optimal cache size.
    
    This widget tries to determine the best cache size based on the widget's
    actual size or specified size parameter.
    """
    
    def __init__(self, **kwargs):
        # Extract size information for smart caching
        size = kwargs.get('size')
        cache_size = None
        
        if size:
            if isinstance(size, (int, float)):
                cache_size = (int(size), int(size))
            elif isinstance(size, (tuple, list)) and len(size) == 2:
                cache_size = (int(size[0]), int(size[1]))
        
        super().__init__(cache_size=cache_size, **kwargs)
    
    def set_size(self, size):
        """Override set_size to update cache size accordingly."""
        if hasattr(super(), 'set_size'):
            super().set_size(size)
        
        # Update cache size based on new size
        if isinstance(size, (int, float)):
            self._cache_size = (int(size), int(size))
        elif isinstance(size, (tuple, list)) and len(size) == 2:
            self._cache_size = (int(size[0]), int(size[1]))


# Convenience functions for creating cached SVG widgets

def create_cached_svg(svg_file: str, size: int = 24, **kwargs) -> CachedSvg:
    """
    Create a cached SVG widget with common defaults.
    
    Args:
        svg_file: Path to the SVG file
        size: Size for the SVG (creates square icon)
        **kwargs: Additional arguments for the widget
    
    Returns:
        CachedSvg widget instance
    """
    return CachedSvg(
        svg_file=svg_file,
        cache_size=(size, size),
        size=size,
        **kwargs
    )


def create_smart_cached_svg(svg_file: str, **kwargs) -> SmartCachedSvg:
    """
    Create a smart cached SVG widget that auto-determines cache size.
    
    Args:
        svg_file: Path to the SVG file
        **kwargs: Additional arguments for the widget
    
    Returns:
        SmartCachedSvg widget instance
    """
    return SmartCachedSvg(svg_file=svg_file, **kwargs)


# Example usage and migration helpers

def migrate_svg_widget(old_svg_widget, cache_size: Optional[Tuple[int, int]] = None) -> CachedSvg:
    """
    Migrate an existing SVG widget to use caching.
    
    Args:
        old_svg_widget: Existing SVG widget
        cache_size: Optional cache size tuple
    
    Returns:
        New CachedSvg widget with same properties
    """
    # Extract properties from old widget
    name = getattr(old_svg_widget, 'name', None)
    visible = old_svg_widget.get_visible()
    
    # Create new cached widget
    new_widget = CachedSvg(
        name=name,
        visible=visible,
        cache_size=cache_size
    )
    
    # Copy any current SVG file
    # Note: This is a simplified migration - you may need to adapt
    # based on your specific widget properties
    
    return new_widget


# Example usage in existing code:

"""
# Before (using standard Svg widget):
from fabric.widgets.svg import Svg

wifi_icon = Svg(
    name="wifi-icon",
    size=24,
    svg_file=get_relative_path("../icons/wifi.svg")
)

# Later in code:
wifi_icon.set_from_file(get_relative_path("../icons/wifi-off.svg"))

# After (using CachedSvg widget):
from utils.cached_svg_widget import CachedSvg

wifi_icon = CachedSvg(
    name="wifi-icon",
    size=24,
    svg_file=get_relative_path("../icons/wifi.svg"),
    cache_size=(24, 24)
)

# Later in code (same API, but now cached):
wifi_icon.set_from_file(get_relative_path("../icons/wifi-off.svg"))

# Or use the explicit cached method:
wifi_icon.set_from_cached_file(get_relative_path("../icons/wifi-off.svg"), (24, 24))

# Or use the convenience function:
wifi_icon = create_cached_svg(
    get_relative_path("../icons/wifi.svg"),
    size=24,
    name="wifi-icon"
)
"""
