"""
Global SVG image caching utility for reducing CPU usage.

This module provides a global cache for SVG images to avoid repeatedly loading
and processing the same SVG files from disk. It's particularly useful for
applications that frequently update SVG icons in widgets.
"""

import os
import threading
from typing import Dict, Optional, Tuple
import weakref

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import GdkPixbuf, GLib
from loguru import logger


class SvgCache:
    """
    A thread-safe global cache for SVG images.
    
    This cache stores processed pixbufs for SVG files to avoid repeated
    file I/O and SVG processing operations. The cache uses weak references
    to automatically clean up unused entries.
    """
    
    def __init__(self):
        self._cache: Dict[str, weakref.ReferenceType] = {}
        self._lock = threading.RLock()
        self._file_timestamps: Dict[str, float] = {}
    
    def _get_cache_key(self, svg_path: str, size: Optional[Tuple[int, int]] = None) -> str:
        """Generate a unique cache key for the SVG file and size."""
        if size:
            return f"{svg_path}:{size[0]}x{size[1]}"
        return svg_path
    
    def _is_file_modified(self, svg_path: str) -> bool:
        """Check if the SVG file has been modified since last cache."""
        try:
            current_mtime = os.path.getmtime(svg_path)
            cached_mtime = self._file_timestamps.get(svg_path, 0)
            return current_mtime > cached_mtime
        except OSError:
            return True  # File doesn't exist or can't be accessed
    
    def _load_svg_pixbuf(self, svg_path: str, size: Optional[Tuple[int, int]] = None) -> Optional[GdkPixbuf.Pixbuf]:
        """Load SVG file and create a pixbuf."""
        try:
            if size:
                width, height = size
                pixbuf = GdkPixbuf.Pixbuf.new_from_file_at_scale(
                    svg_path, width, height, True
                )
            else:
                pixbuf = GdkPixbuf.Pixbuf.new_from_file(svg_path)
            
            # Update file timestamp
            self._file_timestamps[svg_path] = os.path.getmtime(svg_path)
            return pixbuf
            
        except GLib.Error as e:
            logger.warning(f"Failed to load SVG file {svg_path}: {e}")
            return None
        except OSError as e:
            logger.warning(f"Failed to access SVG file {svg_path}: {e}")
            return None
    
    def get_pixbuf(self, svg_path: str, size: Optional[Tuple[int, int]] = None) -> Optional[GdkPixbuf.Pixbuf]:
        """
        Get a cached pixbuf for the SVG file, loading it if necessary.
        
        Args:
            svg_path: Path to the SVG file
            size: Optional tuple of (width, height) for scaling
            
        Returns:
            GdkPixbuf.Pixbuf or None if loading failed
        """
        if not svg_path or not os.path.exists(svg_path):
            return None
        
        cache_key = self._get_cache_key(svg_path, size)
        
        with self._lock:
            # Check if we have a cached version
            if cache_key in self._cache:
                pixbuf_ref = self._cache[cache_key]
                pixbuf = pixbuf_ref()
                
                # If pixbuf is still alive and file hasn't been modified, return it
                if pixbuf is not None and not self._is_file_modified(svg_path):
                    return pixbuf
                
                # Remove dead or outdated reference
                del self._cache[cache_key]
            
            # Load new pixbuf
            pixbuf = self._load_svg_pixbuf(svg_path, size)
            if pixbuf:
                # Store weak reference to allow automatic cleanup
                self._cache[cache_key] = weakref.ref(pixbuf)
            
            return pixbuf
    
    def clear_cache(self):
        """Clear all cached entries."""
        with self._lock:
            self._cache.clear()
            self._file_timestamps.clear()
    
    def remove_file_from_cache(self, svg_path: str):
        """Remove all cached entries for a specific file."""
        with self._lock:
            keys_to_remove = [key for key in self._cache.keys() if key.startswith(svg_path)]
            for key in keys_to_remove:
                del self._cache[key]
            
            if svg_path in self._file_timestamps:
                del self._file_timestamps[svg_path]
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics for debugging."""
        with self._lock:
            alive_entries = sum(1 for ref in self._cache.values() if ref() is not None)
            return {
                "total_entries": len(self._cache),
                "alive_entries": alive_entries,
                "dead_entries": len(self._cache) - alive_entries,
                "tracked_files": len(self._file_timestamps)
            }


# Global cache instance
_svg_cache = SvgCache()


def get_cached_pixbuf(svg_path: str, size: Optional[Tuple[int, int]] = None) -> Optional[GdkPixbuf.Pixbuf]:
    """
    Get a cached pixbuf for an SVG file.
    
    This is the main function to use for getting cached SVG pixbufs.
    
    Args:
        svg_path: Path to the SVG file
        size: Optional tuple of (width, height) for scaling
        
    Returns:
        GdkPixbuf.Pixbuf or None if loading failed
        
    Example:
        # Get pixbuf at original size
        pixbuf = get_cached_pixbuf("/path/to/icon.svg")
        
        # Get pixbuf scaled to 24x24
        pixbuf = get_cached_pixbuf("/path/to/icon.svg", (24, 24))
    """
    return _svg_cache.get_pixbuf(svg_path, size)


def clear_svg_cache():
    """Clear the global SVG cache."""
    _svg_cache.clear_cache()


def remove_svg_from_cache(svg_path: str):
    """Remove a specific SVG file from the cache."""
    _svg_cache.remove_file_from_cache(svg_path)


def get_svg_cache_stats() -> Dict[str, int]:
    """Get SVG cache statistics."""
    return _svg_cache.get_cache_stats()


def set_svg_from_cached_file(svg_widget, svg_path: str, size: Optional[Tuple[int, int]] = None):
    """
    Set an SVG widget's image from a cached file.

    This is a drop-in replacement for svg_widget.set_from_file() that uses caching.

    Args:
        svg_widget: The SVG widget to update
        svg_path: Path to the SVG file
        size: Optional tuple of (width, height) for scaling

    Example:
        # Instead of: svg_widget.set_from_file("/path/to/icon.svg")
        set_svg_from_cached_file(svg_widget, "/path/to/icon.svg")

        # With specific size:
        set_svg_from_cached_file(svg_widget, "/path/to/icon.svg", (24, 24))
    """
    pixbuf = get_cached_pixbuf(svg_path, size)
    if pixbuf:
        # Check if the widget has set_from_pixbuf method
        if hasattr(svg_widget, 'set_from_pixbuf'):
            svg_widget.set_from_pixbuf(pixbuf)
        elif hasattr(svg_widget, 'set_pixbuf'):
            svg_widget.set_pixbuf(pixbuf)
        else:
            # Fallback to original method
            svg_widget.set_from_file(svg_path)
    else:
        # Fallback to original method if caching fails
        try:
            svg_widget.set_from_file(svg_path)
        except Exception as e:
            logger.warning(f"Failed to set SVG from file {svg_path}: {e}")


class CachedSvgMixin:
    """
    Mixin class to add caching functionality to SVG widgets.

    This can be used to extend existing SVG widgets with caching capabilities.
    """

    def set_from_cached_file(self, svg_path: str, size: Optional[Tuple[int, int]] = None):
        """Set the SVG from a cached file."""
        set_svg_from_cached_file(self, svg_path, size)

    def set_from_file_cached(self, svg_path: str):
        """Alias for set_from_cached_file for backward compatibility."""
        self.set_from_cached_file(svg_path)
