# SVG Caching System

## Overview

The SVG caching system provides a global cache for SVG images to reduce CPU usage by avoiding repeated file I/O and SVG processing operations. This is particularly beneficial for applications that frequently update SVG icons in widgets, such as status indicators, battery icons, and other dynamic UI elements.

## Key Features

- **Thread-safe caching**: Uses locks to ensure safe concurrent access
- **Automatic cleanup**: Uses weak references to automatically clean up unused entries
- **File modification detection**: Automatically reloads SVG files when they are modified
- **Size-specific caching**: Caches different sizes of the same SVG separately
- **Fallback support**: Falls back to original loading methods if caching fails
- **Memory efficient**: Automatically removes dead references from cache

## Usage

### Basic Usage

```python
from utils.svg_cache import set_svg_from_cached_file, get_cached_pixbuf

# Instead of:
# svg_widget.set_from_file("/path/to/icon.svg")

# Use:
set_svg_from_cached_file(svg_widget, "/path/to/icon.svg")

# With specific size (recommended for better caching):
set_svg_from_cached_file(svg_widget, "/path/to/icon.svg", (24, 24))
```

### Getting Pixbufs Directly

```python
from utils.svg_cache import get_cached_pixbuf

# Get pixbuf at original size
pixbuf = get_cached_pixbuf("/path/to/icon.svg")

# Get pixbuf scaled to specific size
pixbuf = get_cached_pixbuf("/path/to/icon.svg", (32, 32))

if pixbuf:
    # Use the pixbuf
    image_widget.set_from_pixbuf(pixbuf)
```

### Using the Mixin Class

```python
from utils.svg_cache import CachedSvgMixin
from fabric.widgets.svg import Svg

class CachedSvg(Svg, CachedSvgMixin):
    """SVG widget with built-in caching support."""
    pass

# Usage:
cached_svg = CachedSvg()
cached_svg.set_from_cached_file("/path/to/icon.svg", (24, 24))
```

## Cache Management

### Cache Statistics

```python
from utils.svg_cache import get_svg_cache_stats

stats = get_svg_cache_stats()
print(f"Total entries: {stats['total_entries']}")
print(f"Alive entries: {stats['alive_entries']}")
print(f"Dead entries: {stats['dead_entries']}")
```

### Clearing Cache

```python
from utils.svg_cache import clear_svg_cache, remove_svg_from_cache

# Clear entire cache
clear_svg_cache()

# Remove specific file from cache
remove_svg_from_cache("/path/to/specific/icon.svg")
```

## Implementation Details

### Cache Key Generation

The cache uses a combination of file path and size to generate unique keys:
- Original size: `/path/to/icon.svg`
- Specific size: `/path/to/icon.svg:24x24`

### File Modification Detection

The cache tracks file modification times and automatically reloads SVG files when they are updated. This ensures that changes to SVG files are reflected in the UI without requiring manual cache invalidation.

### Memory Management

The cache uses weak references to store pixbufs, which means:
- When no widgets are using a cached pixbuf, it can be garbage collected
- The cache automatically removes dead references
- Memory usage is kept minimal

### Thread Safety

All cache operations are protected by a reentrant lock (`threading.RLock`), making the cache safe to use from multiple threads.

## Performance Benefits

### Before Caching
- Each `set_from_file()` call loads and processes the SVG from disk
- Repeated calls to the same SVG file result in redundant I/O and processing
- CPU usage spikes during frequent icon updates

### After Caching
- First load processes and caches the SVG
- Subsequent loads use the cached pixbuf
- Significant reduction in CPU usage for frequently updated icons
- Faster UI updates due to eliminated I/O operations

## Best Practices

1. **Specify sizes**: Always specify the target size when caching to optimize memory usage:
   ```python
   set_svg_from_cached_file(widget, path, (24, 24))
   ```

2. **Use consistent sizes**: Use the same size parameters for the same use case to maximize cache hits.

3. **Monitor cache stats**: Periodically check cache statistics in development to ensure optimal cache usage.

4. **Handle fallbacks**: The caching system automatically falls back to original methods if caching fails, but you can add additional error handling if needed.

## Migration Guide

### Updating Existing Code

Replace existing `set_from_file()` calls:

```python
# Before:
self.icon.set_from_file(get_relative_path("../icons/wifi.svg"))

# After:
from utils.svg_cache import set_svg_from_cached_file
set_svg_from_cached_file(
    self.icon, 
    get_relative_path("../icons/wifi.svg"), 
    (24, 24)
)
```

### Batch Updates

For components with multiple SVG updates, consider batching them:

```python
def update_icons(self):
    # All these will benefit from caching
    set_svg_from_cached_file(self.wifi_icon, self.get_wifi_icon_path(), (24, 24))
    set_svg_from_cached_file(self.bt_icon, self.get_bt_icon_path(), (20, 20))
    set_svg_from_cached_file(self.battery_icon, self.get_battery_icon_path(), (24, 24))
```

## Example: Indicators Component

See `modules/panel/components/indicators.py` for a complete example of how the caching system is integrated into the WiFi, Bluetooth, and Battery indicators. The implementation shows:

- Import of the caching function
- Replacement of `set_from_file()` calls with `set_svg_from_cached_file()`
- Consistent size specifications for optimal caching
- Seamless integration with existing code structure

This results in significantly reduced CPU usage when these indicators update frequently, especially during network state changes or battery level updates.
