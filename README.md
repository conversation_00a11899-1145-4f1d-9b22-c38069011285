<p align="center">
  <img src="assets/modus.png" height="200" alt="Logo">
</p>
<p align="center"><img src="https://raw.githubusercontent.com/Tarikul-Islam-Anik/Telegram-Animated-Emojis/main/Activity/Sparkles.webp" alt="Sparkles" width="25" height="25" /> <sup>A modular and extensible shell for Hyprland, written on <a href="https://github.com/Fabric-Development/fabric/">Fabric</a>. </sup><img src="https://raw.githubusercontent.com/Tarikul-Islam-Anik/Telegram-Animated-Emojis/main/Activity/Sparkles.webp" alt="Sparkles" width="25" height="25" /></p>

<p align="center">

<a href="https://github.com/S4NKALP/Modus/graphs/contributors">
<img alt="People" src="https://img.shields.io/github/contributors/S4NKALP/Modus?style=flat&color=ffaaf2&label=People"> </a>

<a href="https://github.com/S4NKALP/Modus/stargazers">
<img alt="Stars" src="https://img.shields.io/github/stars/S4NKALP/Modus?style=flat&color=98c379&label=Stars"></a>

<a href="https://github.com/S4NKALP/Modus/network/members">
<img alt="Forks" src="https://img.shields.io/github/forks/S4NKALP/Modus?style=flat&color=66a8e0&label=Forks"> </a>

<a href="https://github.com/S4NKALP/Modus/watchers">
<img alt="Watches" src="https://img.shields.io/github/watchers/S4NKALP/Modus?style=flat&color=f5d08b&label=Watches"> </a>

<a href="https://github.com/S4NKALP/Modus/pulse">
<img alt="Last Updated" src="https://img.shields.io/github/last-commit/S4NKALP/Modus?style=flat&color=e06c75&label="> </a>
</p>

<br>

<figure>
  <img src="assets/screenshots/home.png" alt="fabric">
  <br/>
</figure>
<br>

<h2><sub><img src="https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Travel%20and%20places/Rocket.png" alt="Rocket" width="25" height="25" /></sub> Todo</h2>

- [x] Launcher
- [x] Dock
- [ ] Application Switcher
- [ ] Notification
- [ ] Control Center
- [ ] Panel Widget

## Special Thanks

A big thank you to the following people for their incredible help with code and creative ideas. Your help made a real difference!

- [darsh](https://github.com/its-darsh): for creating Fabric, which made everything possible.
- [gummy bear album](https://github.com/muhchaudhary): for sharing fantastic code snippets that saved me time and effort.
- [axenide](https://github.com/Axenide): for the amazing config that not only inspired parts of mine but also provided some gems I couldn’t resist borrowing.

I truly appreciate your support
